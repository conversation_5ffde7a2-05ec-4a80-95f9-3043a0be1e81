import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'novel.g.dart';

// 小说类型枚举
enum NovelType {
  longNovel('长篇小说'),
  shortNovel('短篇小说');

  const NovelType(this.displayName);
  final String displayName;
}

// 短篇小说字数选项
enum ShortNovelWordCount {
  words3000(3000, '3000字'),
  words5000(5000, '5000字'),
  words8000(8000, '8000字'),
  words10000(10000, '10000字'),
  words15000(15000, '15000字'),
  words20000(20000, '20000字'),
  words30000(30000, '30000字');

  const ShortNovelWordCount(this.count, this.displayName);
  final int count;
  final String displayName;
}

// 短篇小说大纲部分
class ShortNovelOutlinePart {
  final int partNumber;
  final String title;
  final String description;
  final int startPercentage;
  final int endPercentage;
  final String detailedOutline; // 细纲内容

  ShortNovelOutlinePart({
    required this.partNumber,
    required this.title,
    required this.description,
    required this.startPercentage,
    required this.endPercentage,
    this.detailedOutline = '',
  });

  Map<String, dynamic> toJson() => {
        'partNumber': partNumber,
        'title': title,
        'description': description,
        'startPercentage': startPercentage,
        'endPercentage': endPercentage,
        'detailedOutline': detailedOutline,
      };

  factory ShortNovelOutlinePart.fromJson(Map<String, dynamic> json) =>
      ShortNovelOutlinePart(
        partNumber: json['partNumber'] as int,
        title: json['title'] as String,
        description: json['description'] as String,
        startPercentage: json['startPercentage'] as int,
        endPercentage: json['endPercentage'] as int,
        detailedOutline: json['detailedOutline'] as String? ?? '',
      );

  ShortNovelOutlinePart copyWith({
    int? partNumber,
    String? title,
    String? description,
    int? startPercentage,
    int? endPercentage,
    String? detailedOutline,
  }) {
    return ShortNovelOutlinePart(
      partNumber: partNumber ?? this.partNumber,
      title: title ?? this.title,
      description: description ?? this.description,
      startPercentage: startPercentage ?? this.startPercentage,
      endPercentage: endPercentage ?? this.endPercentage,
      detailedOutline: detailedOutline ?? this.detailedOutline,
    );
  }
}

// 短篇小说大纲
class ShortNovelOutline {
  final String title;
  final List<ShortNovelOutlinePart> parts;
  final int totalWordCount;
  final int totalParts;

  ShortNovelOutline({
    required this.title,
    required this.parts,
    required this.totalWordCount,
    required this.totalParts,
  });

  Map<String, dynamic> toJson() => {
        'title': title,
        'parts': parts.map((part) => part.toJson()).toList(),
        'totalWordCount': totalWordCount,
        'totalParts': totalParts,
      };

  factory ShortNovelOutline.fromJson(Map<String, dynamic> json) =>
      ShortNovelOutline(
        title: json['title'] as String,
        parts: (json['parts'] as List)
            .map((part) =>
                ShortNovelOutlinePart.fromJson(part as Map<String, dynamic>))
            .toList(),
        totalWordCount: json['totalWordCount'] as int,
        totalParts: json['totalParts'] as int,
      );

  ShortNovelOutline copyWith({
    String? title,
    List<ShortNovelOutlinePart>? parts,
    int? totalWordCount,
    int? totalParts,
  }) {
    return ShortNovelOutline(
      title: title ?? this.title,
      parts: parts ?? this.parts,
      totalWordCount: totalWordCount ?? this.totalWordCount,
      totalParts: totalParts ?? this.totalParts,
    );
  }
}

@HiveType(typeId: 0)
class Novel {
  @HiveField(0)
  final String id;

  @HiveField(1)
  String title;

  @HiveField(2)
  final String genre;

  @HiveField(3)
  String outline;

  @HiveField(4)
  String content;

  @HiveField(5)
  final List<Chapter> chapters;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final DateTime? updatedAt;

  @HiveField(8)
  final String? style;

  @HiveField(9)
  final String? sessionId;

  @HiveField(10)
  final String? folderPath; // 小说文件夹路径

  @HiveField(11)
  final bool useFileSystem; // 是否使用文件系统存储

  @HiveField(12)
  final String? author; // 作者

  @HiveField(13)
  final String? status; // 状态：ongoing(连载中), completed(已完结), paused(暂停)

  @HiveField(14)
  final String? generationStatus; // 生成状态：notStarted, generating, paused, interrupted, completed

  String get createTime => createdAt.toString().split('.')[0];

  int get wordCount => content.replaceAll(RegExp(r'\s'), '').length;

  // 获取生成状态的显示文本
  String get generationStatusText {
    switch (generationStatus) {
      case 'notStarted':
        return '未开始';
      case 'generating':
        return '生成中';
      case 'paused':
        return '已暂停';
      case 'interrupted':
        return '中断';
      case 'completed':
        return '已完成';
      default:
        return '未知';
    }
  }

  // 获取生成状态的颜色
  String get generationStatusColor {
    switch (generationStatus) {
      case 'notStarted':
        return 'info';
      case 'generating':
        return 'primary';
      case 'paused':
        return 'warning';
      case 'interrupted':
        return 'danger';
      case 'completed':
        return 'success';
      default:
        return 'info';
    }
  }

  Novel({
    String? id,
    String? title,
    String? genre,
    String? outline,
    String? content,
    required this.chapters,
    required this.createdAt,
    this.updatedAt,
    this.style,
    this.sessionId,
    this.folderPath,
    this.useFileSystem = false,
    this.author,
    this.status,
    this.generationStatus, // 生成状态，可为空以保持向后兼容
  }) : id = id ?? const Uuid().v4(),
       title = title ?? '未命名小说',
       genre = genre ?? '其他',
       outline = outline ?? '',
       content = content ?? '';

  Novel copyWith({
    String? title,
    String? genre,
    String? outline,
    String? content,
    List<Chapter>? chapters,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? style,
    String? id,
    String? sessionId,
    String? folderPath,
    bool? useFileSystem,
    String? author,
    String? status,
    String? generationStatus,
  }) {
    return Novel(
      title: title ?? this.title,
      genre: genre ?? this.genre,
      outline: outline ?? this.outline,
      content: content ?? this.content,
      chapters: chapters ?? this.chapters,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      style: style ?? this.style,
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      folderPath: folderPath ?? this.folderPath,
      useFileSystem: useFileSystem ?? this.useFileSystem,
      author: author ?? this.author,
      status: status ?? this.status,
      generationStatus: generationStatus ?? this.generationStatus,
    );
  }

  Map<String, dynamic> toJson() {
    try {
      print('🔍 开始序列化小说: $title');

      // 检查基本字段
      print('   ID: $id');
      print('   标题: $title');
      print('   类型: $genre');
      print('   大纲长度: ${outline.length}');
      print('   内容长度: ${content.length}');

      // 安全检查status字段
      print('   状态: ${status ?? 'ongoing'}');

      // 安全检查generationStatus字段
      try {
        print('   生成状态: $generationStatus');
      } catch (e) {
        print('   ❌ 生成状态字段访问失败: $e');
        print('   生成状态类型: ${generationStatus.runtimeType}');
      }

      // 安全处理章节序列化
      List<Map<String, dynamic>> chaptersJson = [];
      print('   开始处理 ${chapters.length} 个章节...');

      for (int i = 0; i < chapters.length; i++) {
        try {
          print('     处理章节 ${i + 1}: ${chapters[i].title}');
          final chapterJson = chapters[i].toJson();
          chaptersJson.add(chapterJson);
          print('     ✅ 章节 ${i + 1} 序列化成功');
        } catch (e) {
          print('❌ 章节 ${i + 1} 序列化失败: $e');
          print('   章节标题: ${chapters[i].title}');
          print('   章节编号: ${chapters[i].number}');
          // 跳过有问题的章节，继续处理其他章节
        }
      }

      print('   章节处理完成，成功: ${chaptersJson.length}/${chapters.length}');

      // 逐个构建返回对象，便于定位问题
      final result = <String, dynamic>{};

      try {
        result['id'] = id;
        result['title'] = title;
        result['genre'] = genre;
        result['outline'] = outline;
        result['content'] = content;
        result['chapters'] = chaptersJson;
        result['createdAt'] = createdAt.toIso8601String();
        result['updatedAt'] = updatedAt?.toIso8601String();
        result['style'] = style;
        result['sessionId'] = sessionId;
        result['folderPath'] = folderPath;
        result['useFileSystem'] = useFileSystem;
        result['author'] = author ?? '';

        // 安全处理status字段
        result['status'] = status ?? 'ongoing';

        // 安全处理generationStatus字段
        result['generationStatus'] = generationStatus ?? 'notStarted';

        result['wordCount'] = wordCount;
        result['chapterCount'] = chaptersJson.length;
      } catch (e) {
        print('   ❌ 构建返回对象失败: $e');
        rethrow;
      }

      print('✅ 小说序列化成功: $title');
      return result;

    } catch (e) {
      print('❌ Novel.toJson() 序列化失败: $e');
      print('   小说标题: $title');
      print('   小说ID: $id');
      print('   章节数量: ${chapters.length}');
      rethrow;
    }
  }

  factory Novel.fromJson(Map<String, dynamic> json) => Novel(
        id: json['id'] as String? ??
            'unknown_${DateTime.now().millisecondsSinceEpoch}',
        title: json['title'] as String? ?? '未命名小说',
        genre: json['genre'] as String? ?? '其他',
        outline: json['outline'] as String? ?? '暂无大纲',
        content: json['content'] as String? ?? '',
        chapters: (json['chapters'] as List? ?? [])
            .map((c) => Chapter.fromJson(c as Map<String, dynamic>))
            .toList(),
        createdAt: json['createdAt'] != null
            ? DateTime.parse(json['createdAt'] as String)
            : DateTime.now(),
        updatedAt: json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'] as String)
            : null,
        style: json['style'] as String?,
        sessionId: json['sessionId'] as String?,
        folderPath: json['folderPath'] as String?,
        useFileSystem: json['useFileSystem'] as bool? ?? false,
        author: json['author'] as String?,
        status: json['status'] as String? ?? 'ongoing',
        generationStatus: json['generationStatus'] as String?, // 可为空，保持向后兼容
      );

  Chapter get outlineChapter {
    // 不再从章节列表中查找第0章
    // 直接返回包含大纲内容的临时章节对象
    return Chapter(
      number: 0,
      title: '大纲',
      content: outline,
    );
  }

  void addOutlineAsChapter() {
    // 此功能已弃用，不再将大纲保存为第0章
    // 为保持兼容性，保留此方法但不执行任何操作
    /*
    if (!chapters.any((chapter) => chapter.number == 0)) {
      chapters.insert(0, Chapter(
        number: 0,
        title: '大纲',
        content: outline,
      ));
    }
    */
  }
}

@HiveType(typeId: 1)
class Chapter {
  @HiveField(0)
  final int number;

  @HiveField(1)
  final String title;

  @HiveField(2)
  String content;

  int get index => number - 1;

  Chapter({
    required this.number,
    required this.title,
    required this.content,
  });

  Chapter copyWith({
    int? number,
    String? title,
    String? content,
  }) {
    return Chapter(
      number: number ?? this.number,
      title: title ?? this.title,
      content: content ?? this.content,
    );
  }

  Map<String, dynamic> toJson() {
    try {
      print('       🔍 序列化章节: $number - $title');
      print('         内容长度: ${content.length}');

      final result = {
        'number': number,
        'title': title,
        'content': content,
      };

      print('       ✅ 章节序列化完成');
      return result;
    } catch (e) {
      print('❌ Chapter.toJson() 序列化失败: $e');
      print('   章节编号: $number');
      print('   章节标题: $title');
      print('   内容类型: ${content.runtimeType}');
      print('   标题类型: ${title.runtimeType}');
      rethrow;
    }
  }

  factory Chapter.fromJson(Map<String, dynamic> json) => Chapter(
        number: json['number'] as int? ?? 1,
        title: json['title'] as String? ?? '未命名章节',
        content: json['content'] as String? ?? '',
      );
}
