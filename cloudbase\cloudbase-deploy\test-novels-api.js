#!/usr/bin/env node

/**
 * 测试小说API接口
 */

const https = require('https');

// 测试获取小说列表API
async function testNovelsAPI() {
  console.log('🧪 测试小说列表API...');
  
  const options = {
    hostname: 'api.dznovel.top',
    port: 443,
    path: '/api/novels?_api_path=novels',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-admin-token' // 使用测试token
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      console.log(`状态码: ${res.statusCode}`);
      console.log(`响应头:`, res.headers);

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('📊 API响应:');
          console.log(JSON.stringify(result, null, 2));
          resolve(result);
        } catch (error) {
          console.log('📄 原始响应:', data);
          resolve(data);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ 请求错误:', error);
      reject(error);
    });

    req.end();
  });
}

// 测试管理员登录
async function testAdminLogin() {
  console.log('🔐 测试管理员登录...');
  
  const postData = JSON.stringify({
    username: 'admin',
    password: 'your-secure-admin-password'
  });

  const options = {
    hostname: 'api.dznovel.top',
    port: 443,
    path: '/api/admin/login?_api_path=admin/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      console.log(`状态码: ${res.statusCode}`);

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('🔑 登录响应:');
          console.log(JSON.stringify(result, null, 2));
          resolve(result);
        } catch (error) {
          console.log('📄 原始响应:', data);
          resolve(data);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ 登录请求错误:', error);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// 使用真实token测试小说API
async function testNovelsWithRealToken(token) {
  console.log('🧪 使用真实token测试小说列表API...');
  
  const options = {
    hostname: 'api.dznovel.top',
    port: 443,
    path: '/api/novels?_api_path=novels',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      console.log(`状态码: ${res.statusCode}`);

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('📚 小说列表响应:');
          console.log(`总数: ${result.data?.total || 0}`);
          console.log(`小说数量: ${result.data?.novels?.length || 0}`);
          if (result.data?.novels?.length > 0) {
            console.log('前几本小说:');
            result.data.novels.slice(0, 3).forEach((novel, i) => {
              console.log(`  ${i + 1}. ${novel.title} (作者: ${novel.author})`);
            });
          }
          resolve(result);
        } catch (error) {
          console.log('📄 原始响应:', data);
          resolve(data);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ 请求错误:', error);
      reject(error);
    });

    req.end();
  });
}

// 主测试函数
async function main() {
  try {
    console.log('=== 小说API测试 ===\n');
    
    // 1. 先测试管理员登录获取token
    const loginResult = await testAdminLogin();
    
    if (loginResult.success && loginResult.data?.token) {
      console.log('\n✅ 登录成功，获取到token\n');
      
      // 2. 使用真实token测试小说API
      await testNovelsWithRealToken(loginResult.data.token);
    } else {
      console.log('\n❌ 登录失败，使用测试token\n');
      
      // 3. 使用测试token（可能会失败，但能看到错误信息）
      await testNovelsAPI();
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
main();
