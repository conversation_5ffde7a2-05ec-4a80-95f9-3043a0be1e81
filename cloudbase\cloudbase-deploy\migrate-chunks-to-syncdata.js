#!/usr/bin/env node

/**
 * 岱宗文脉 - 数据迁移脚本
 * 将user_data_chunks集合中的分块数据迁移到users集合的syncData字段
 */

const cloudbase = require('@cloudbase/node-sdk');

// 初始化CloudBase - 使用环境变量或默认配置
const app = cloudbase.init({
  env: 'novel-app-2gywkgnn15cbd6a8'
});

const db = app.database();

// 日志函数
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 从分块数据重组完整数据
async function reconstructDataFromChunks(userId) {
  try {
    log(`🔄 重组用户 ${userId} 的分块数据...`, 'blue');

    // 获取用户的所有数据块，按索引排序
    const chunks = await db.collection('user_data_chunks').where({
      userId: userId
    }).orderBy('chunkIndex', 'asc').get();

    if (chunks.data.length === 0) {
      log(`⚠️  用户 ${userId} 没有分块数据`, 'yellow');
      return null;
    }

    log(`📦 找到 ${chunks.data.length} 个数据块`, 'cyan');

    // 重组数据
    let dataString = '';
    for (const chunk of chunks.data) {
      dataString += chunk.chunkData;
    }

    const data = JSON.parse(dataString);
    log(`✅ 数据重组完成，总大小: ${(dataString.length / 1024).toFixed(2)} KB`, 'green');
    
    return {
      data: data,
      timestamp: chunks.data[0].timestamp,
      totalSize: dataString.length
    };
  } catch (error) {
    log(`❌ 重组数据失败: ${error.message}`, 'red');
    return null;
  }
}

// 迁移单个用户的数据
async function migrateUserData(userId) {
  try {
    log(`\n🎯 开始迁移用户: ${userId}`, 'blue');

    // 重组分块数据
    const reconstructedData = await reconstructDataFromChunks(userId);
    if (!reconstructedData) {
      log(`⚠️  用户 ${userId} 没有需要迁移的数据`, 'yellow');
      return false;
    }

    // 查找用户记录
    const userResult = await db.collection('users').where({
      userId: userId
    }).get();

    if (userResult.data.length === 0) {
      log(`❌ 未找到用户记录: ${userId}`, 'red');
      return false;
    }

    const user = userResult.data[0];
    log(`👤 找到用户: ${user.username || '未知用户'}`, 'cyan');

    // 检查用户是否已有syncData
    if (user.syncData && user.syncData.novels && user.syncData.novels.length > 0) {
      log(`⚠️  用户已有syncData (${user.syncData.novels.length}本小说)，跳过迁移`, 'yellow');
      return false;
    }

    // 更新用户的syncData字段
    await db.collection('users').doc(user._id).update({
      syncData: reconstructedData.data,
      syncTimestamp: reconstructedData.timestamp,
      syncUpdatedAt: new Date().toISOString(),
      lastSyncSize: reconstructedData.totalSize,
      syncMethod: 'migrated_from_chunks'
    });

    log(`✅ 用户数据迁移成功`, 'green');
    
    // 统计迁移的小说数量
    if (reconstructedData.data.novels) {
      log(`📚 迁移了 ${reconstructedData.data.novels.length} 本小说`, 'green');
    }

    return true;
  } catch (error) {
    log(`❌ 迁移用户数据失败: ${error.message}`, 'red');
    return false;
  }
}

// 清理分块数据
async function cleanupChunks(userId) {
  try {
    log(`🧹 清理用户 ${userId} 的分块数据...`, 'blue');
    
    const result = await db.collection('user_data_chunks').where({
      userId: userId
    }).remove();

    log(`✅ 清理完成，删除了 ${result.deleted} 个数据块`, 'green');
    return true;
  } catch (error) {
    log(`❌ 清理分块数据失败: ${error.message}`, 'red');
    return false;
  }
}

// 主迁移函数
async function migrate() {
  log('=== 数据迁移工具：user_data_chunks -> users.syncData ===', 'blue');
  
  try {
    // 获取所有有分块数据的用户ID
    const chunksResult = await db.collection('user_data_chunks').get();
    const chunks = chunksResult.data || [];

    if (chunks.length === 0) {
      log('✅ 没有需要迁移的分块数据', 'green');
      return;
    }

    // 获取唯一的用户ID列表
    const userIds = [...new Set(chunks.map(chunk => chunk.userId))];
    log(`📊 发现 ${userIds.length} 个用户有分块数据需要迁移`, 'blue');

    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    // 逐个迁移用户数据
    for (const userId of userIds) {
      const success = await migrateUserData(userId);
      if (success) {
        successCount++;
        // 迁移成功后清理分块数据
        await cleanupChunks(userId);
      } else {
        skipCount++;
      }
    }

    log('\n=== 迁移完成 ===', 'green');
    log(`✅ 成功迁移: ${successCount} 个用户`, 'green');
    log(`⚠️  跳过: ${skipCount} 个用户`, 'yellow');
    log(`❌ 失败: ${errorCount} 个用户`, 'red');

    // 检查是否还有剩余的分块数据
    const remainingChunks = await db.collection('user_data_chunks').get();
    if (remainingChunks.data.length === 0) {
      log('🎉 所有分块数据已成功迁移并清理！', 'green');
    } else {
      log(`⚠️  还有 ${remainingChunks.data.length} 个分块数据未清理`, 'yellow');
    }

  } catch (error) {
    log(`❌ 迁移过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 运行迁移
migrate().then(() => {
  log('迁移脚本执行完成', 'blue');
  process.exit(0);
}).catch(error => {
  log(`迁移脚本执行失败: ${error.message}`, 'red');
  process.exit(1);
});
