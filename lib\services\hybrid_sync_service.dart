import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';
import '../services/auth_service.dart';
import '../services/user_sync_service.dart';
import '../services/cloudbase_sync_service.dart';

/// 同步任务状态
enum SyncTaskStatus {
  waiting, // 等待中
  running, // 进行中
  completed, // 完成
  failed, // 失败
  retrying // 重试中
}

/// 同步任务类型
enum SyncTaskType {
  userSettings, // 用户设置
  novels, // 小说数据
  knowledgeBase, // 知识库
  characters, // 角色数据
  writingStyles, // 文风包
  full // 全量同步
}

/// 同步任务
class SyncTask {
  final String id;
  final SyncTaskType type;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  SyncTaskStatus status;
  String? errorMessage;
  int retryCount;
  DateTime? completedAt;
  double progress;

  SyncTask({
    required this.id,
    required this.type,
    required this.data,
    required this.createdAt,
    this.status = SyncTaskStatus.waiting,
    this.errorMessage,
    this.retryCount = 0,
    this.completedAt,
    this.progress = 0.0,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type.name,
        'status': status.name,
        'errorMessage': errorMessage,
        'retryCount': retryCount,
        'progress': progress,
        'createdAt': createdAt.toIso8601String(),
        'completedAt': completedAt?.toIso8601String(),
      };
}

/// 数据指纹信息
class DataFingerprint {
  final String dataType;
  final String hash;
  final DateTime lastModified;
  final int itemCount;
  final DateTime? lastSyncTime; // 新增：上次同步时间

  DataFingerprint({
    required this.dataType,
    required this.hash,
    required this.lastModified,
    required this.itemCount,
    this.lastSyncTime, // 可选参数
  });

  Map<String, dynamic> toJson() => {
        'dataType': dataType,
        'hash': hash,
        'lastModified': lastModified.toIso8601String(),
        'itemCount': itemCount,
        'lastSyncTime': lastSyncTime?.toIso8601String(),
      };

  factory DataFingerprint.fromJson(Map<String, dynamic> json) =>
      DataFingerprint(
        dataType: json['dataType'],
        hash: json['hash'],
        lastModified: DateTime.parse(json['lastModified']),
        itemCount: json['itemCount'],
        lastSyncTime: json['lastSyncTime'] != null
            ? DateTime.parse(json['lastSyncTime'])
            : null,
      );

  /// 创建带有新同步时间的副本
  DataFingerprint copyWithSyncTime(DateTime syncTime) {
    return DataFingerprint(
      dataType: dataType,
      hash: hash,
      lastModified: lastModified,
      itemCount: itemCount,
      lastSyncTime: syncTime,
    );
  }
}

/// 混合同步服务 - CloudBase数据库存储 + 增量同步 + 队列管理
class HybridSyncService extends GetxService {
  AuthService? _authService;

  // 响应式状态
  final RxBool isInitialized = false.obs;
  final RxBool isSyncing = false.obs;
  final RxDouble syncProgress = 0.0.obs;
  final RxString syncStatus = '准备就绪'.obs;
  final RxList<SyncTask> taskQueue = <SyncTask>[].obs;
  final RxMap<String, DataFingerprint> dataFingerprints =
      <String, DataFingerprint>{}.obs;

  // 配置
  String get _baseUrl => ApiConfig.baseUrl;

  // 内部状态
  Timer? _syncTimer;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initialize();
  }

  @override
  void onClose() {
    _syncTimer?.cancel();
    super.onClose();
  }

  /// 初始化服务
  Future<void> _initialize() async {
    try {
      print('🔧 HybridSyncService 初始化开始...');

      // 加载本地数据指纹
      await _loadDataFingerprints();

      // 加载未完成的任务队列
      await _loadTaskQueue();

      // 启动定时同步（每30分钟）
      _startPeriodicSync();

      isInitialized.value = true;
      syncStatus.value = '同步服务已就绪';

      print('✅ HybridSyncService 初始化完成');
    } catch (e) {
      print('❌ HybridSyncService 初始化失败: $e');
      syncStatus.value = '初始化失败';
    }
  }

  /// 启动定时同步
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 30), (timer) {
      if (_getAuthService.isLoggedIn.value && !isSyncing.value) {
        syncAllData();
      }
    });
  }

  /// 加载本地数据指纹
  Future<void> _loadDataFingerprints() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final fingerprintsJson = prefs.getString('data_fingerprints');

      if (fingerprintsJson != null) {
        final Map<String, dynamic> data = jsonDecode(fingerprintsJson);
        dataFingerprints.clear();

        data.forEach((key, value) {
          dataFingerprints[key] = DataFingerprint.fromJson(value);
        });

        print('📊 加载了 ${dataFingerprints.length} 个数据指纹');
      }
    } catch (e) {
      print('❌ 加载数据指纹失败: $e');
    }
  }

  /// 保存数据指纹
  Future<void> _saveDataFingerprints() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final Map<String, dynamic> data = {};

      dataFingerprints.forEach((key, value) {
        data[key] = value.toJson();
      });

      await prefs.setString('data_fingerprints', jsonEncode(data));
    } catch (e) {
      print('❌ 保存数据指纹失败: $e');
    }
  }

  /// 加载任务队列
  Future<void> _loadTaskQueue() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getString('sync_task_queue');

      if (queueJson != null) {
        final List<dynamic> data = jsonDecode(queueJson);
        taskQueue.clear();

        // 只加载未完成的任务
        for (final taskData in data) {
          if (taskData['status'] != 'completed') {
            // 重新创建任务对象（简化版，实际需要完整重建）
            print('📋 发现未完成任务: ${taskData['type']}');
          }
        }
      }
    } catch (e) {
      print('❌ 加载任务队列失败: $e');
    }
  }

  /// 保存任务队列
  Future<void> _saveTaskQueue() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<Map<String, dynamic>> data =
          taskQueue.map((task) => task.toJson()).toList();
      await prefs.setString('sync_task_queue', jsonEncode(data));
    } catch (e) {
      print('❌ 保存任务队列失败: $e');
    }
  }

  /// 计算数据指纹
  String _calculateDataHash(Map<String, dynamic> data) {
    final dataString = jsonEncode(data);
    final bytes = utf8.encode(dataString);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 检查数据是否需要同步
  bool _needsSync(String dataType, Map<String, dynamic> data) {
    final currentHash = _calculateDataHash(data);
    final existingFingerprint = dataFingerprints[dataType];

    if (existingFingerprint == null) {
      print('🔍 $dataType: 首次同步');
      return true;
    }

    if (existingFingerprint.hash != currentHash) {
      print('🔍 $dataType: 数据已变更');
      return true;
    }

    print('🔍 $dataType: 数据无变化，跳过同步');
    return false;
  }

  /// 检查小说是否需要同步（基于修改时间和云端对比）
  Future<bool> _needsSyncByTime(
      String dataType, Map<String, dynamic> data) async {
    final existingFingerprint = dataFingerprints[dataType];

    if (existingFingerprint == null) {
      print('🔍 $dataType: 首次同步');
      return true;
    }

    // 对于小说数据，使用云端对比逻辑
    if (dataType == 'novels' &&
        data['data'] != null &&
        data['data']['novels'] != null) {
      return await _checkNovelsNeedSyncWithCloud(
          data['data']['novels'], existingFingerprint.lastSyncTime);
    }

    // 其他数据类型仍使用哈希检查
    return _needsSync(dataType, data);
  }

  /// 获取云端数据进行对比（新增方法）
  Future<Map<String, dynamic>?> _getCloudData() async {
    try {
      print('🔑 开始获取认证token...');
      final token = await _getToken();
      if (token == null) {
        print('❌ 获取token失败，无法获取云端数据');
        return null;
      }
      print('✅ Token获取成功');

      if (Get.isRegistered<CloudBaseSyncService>()) {
        print('📡 开始从CloudBase下载数据...');
        final cloudbaseService = Get.find<CloudBaseSyncService>();
        final cloudData = await cloudbaseService.downloadData(token);

        if (cloudData != null) {
          final novelsCount = (cloudData['novels'] as List?)?.length ?? 0;
          print('✅ 成功获取云端数据，包含 $novelsCount 本小说');
          return cloudData;
        } else {
          print('ℹ️ 云端返回空数据');
          return null;
        }
      } else {
        print('❌ CloudBaseSyncService未注册');
        return null;
      }
    } catch (e) {
      print('❌ 获取云端数据异常: $e');
      return null;
    }
  }

  /// 对比本地小说与云端小说，返回同步原因
  String? _compareNovelWithCloud(
      Map<String, dynamic> localNovel, List cloudNovels) {
    try {
      // 在云端查找相同的小说（通过title或id匹配）
      Map<String, dynamic>? cloudNovel;

      for (final novel in cloudNovels) {
        if (novel is Map<String, dynamic>) {
          // 优先通过ID匹配
          if (localNovel['id'] != null &&
              novel['id'] != null &&
              localNovel['id'] == novel['id']) {
            cloudNovel = novel;
            break;
          }
          // 其次通过标题匹配
          if (localNovel['title'] == novel['title']) {
            cloudNovel = novel;
            break;
          }
        }
      }

      if (cloudNovel == null) {
        return '云端不存在，需要上传';
      }

      // 对比修改时间
      final localUpdatedStr = localNovel['updatedAt'] as String?;
      final cloudUpdatedStr = cloudNovel['updatedAt'] as String?;

      if (localUpdatedStr != null && cloudUpdatedStr != null) {
        final localUpdated = DateTime.parse(localUpdatedStr);
        final cloudUpdated = DateTime.parse(cloudUpdatedStr);

        if (localUpdated.isAfter(cloudUpdated)) {
          return '本地更新 (${localUpdated.toString().split('.')[0]} > ${cloudUpdated.toString().split('.')[0]})';
        } else if (cloudUpdated.isAfter(localUpdated)) {
          return '云端更新 (需要下载)';
        }
      } else if (localUpdatedStr != null && cloudUpdatedStr == null) {
        return '本地有更新时间，云端无';
      } else if (localUpdatedStr == null && cloudUpdatedStr != null) {
        return '云端有更新时间，本地无';
      }

      // 对比章节数量
      final localChapters = localNovel['chapters'] as List?;
      final cloudChapters = cloudNovel['chapters'] as List?;

      final localChapterCount = localChapters?.length ?? 0;
      final cloudChapterCount = cloudChapters?.length ?? 0;

      if (localChapterCount != cloudChapterCount) {
        return '章节数量不同 (本地:$localChapterCount, 云端:$cloudChapterCount)';
      }

      // 对比章节内容（简化版：只检查最后一章的修改时间）
      if (localChapters != null &&
          cloudChapters != null &&
          localChapters.isNotEmpty &&
          cloudChapters.isNotEmpty) {
        final lastLocalChapter = localChapters.last;
        final lastCloudChapter = cloudChapters.last;

        final localChapterTime = lastLocalChapter['createTime'] as String?;
        final cloudChapterTime = lastCloudChapter['createTime'] as String?;

        if (localChapterTime != null && cloudChapterTime != null) {
          final localTime = DateTime.parse(localChapterTime);
          final cloudTime = DateTime.parse(cloudChapterTime);

          if (localTime.isAfter(cloudTime)) {
            return '最新章节时间不同';
          }
        }
      }

      return null; // 无需同步
    } catch (e) {
      print('❌ 对比小说数据异常: $e');
      return '对比异常，安全同步';
    }
  }

  /// 检查小说列表中哪些需要同步（使用云端对比）
  Future<bool> _checkNovelsNeedSyncWithCloud(
      List novels, DateTime? lastSyncTime) async {
    if (lastSyncTime == null) {
      print('🔍 小说: 首次同步，需要同步所有 ${novels.length} 本小说');
      return true;
    }

    try {
      // 获取云端数据进行对比
      print('🔍 获取云端数据进行对比...');
      final cloudData = await _getCloudData();
      final cloudNovels = cloudData?['novels'] as List?;

      if (cloudNovels == null || cloudNovels.isEmpty) {
        // 云端无数据，应该同步所有本地数据
        print('🔍 云端无数据，需要同步所有本地小说到云端...');
        print('📊 同步结果: ${novels.length}/${novels.length} 本小说需要同步（云端为空）');
        return true;
      } else {
        // 对比本地和云端数据
        print('🔍 对比本地 ${novels.length} 本小说与云端 ${cloudNovels.length} 本小说...');
        int needSyncCount = 0;
        for (final localNovel in novels) {
          final syncReason = _compareNovelWithCloud(localNovel, cloudNovels);
          if (syncReason != null) {
            needSyncCount++;
            print('📝 需要同步: ${localNovel['title']} ($syncReason)');
          }
        }

        if (needSyncCount > 0) {
          print('📊 同步结果: $needSyncCount/${novels.length} 本小说需要同步');
          return true;
        } else {
          print('🔍 小说: 所有小说都是最新的，跳过同步');
          return false;
        }
      }
    } catch (e) {
      print('❌ 检查小说同步状态失败: $e，默认需要同步');
      return true; // 出错时默认需要同步
    }
  }

  /// 检查单本小说是否在指定时间后被修改
  bool _isNovelModifiedAfter(
      Map<String, dynamic> novel, DateTime lastSyncTime) {
    try {
      // 检查小说的更新时间
      final updatedAtStr = novel['updatedAt'] as String?;
      if (updatedAtStr != null) {
        final updatedAt = DateTime.parse(updatedAtStr);
        if (updatedAt.isAfter(lastSyncTime)) {
          print(
              '📝 小说 "${novel['title']}" 需要同步: 更新时间 $updatedAt > 上次同步时间 $lastSyncTime');
          return true;
        }
      }

      // 检查章节的创建/更新时间
      final chapters = novel['chapters'] as List?;
      if (chapters != null) {
        for (final chapter in chapters) {
          // 优先检查 updatedAt，如果没有则使用 createTime
          String? timeStr = chapter['updatedAt'] as String?;
          timeStr ??= chapter['createTime'] as String?;

          if (timeStr != null) {
            try {
              final chapterTime = DateTime.parse(timeStr);
              if (chapterTime.isAfter(lastSyncTime)) {
                print(
                    '📄 小说 "${novel['title']}" 需要同步: 章节 "${chapter['title']}" 时间 $chapterTime > 上次同步时间 $lastSyncTime');
                return true;
              }
            } catch (e) {
              // 如果时间解析失败，默认需要同步
              print('⚠️ 章节时间解析失败: $timeStr，默认需要同步');
              return true;
            }
          }
        }
      }

      return false;
    } catch (e) {
      print('⚠️ 检查小说修改时间失败: $e，默认需要同步');
      return true; // 出错时默认需要同步
    }
  }

  /// 更新数据指纹
  void _updateDataFingerprint(String dataType, Map<String, dynamic> data) {
    final hash = _calculateDataHash(data);
    final itemCount = _getDataItemCount(data);

    dataFingerprints[dataType] = DataFingerprint(
      dataType: dataType,
      hash: hash,
      lastModified: DateTime.now(),
      itemCount: itemCount,
      lastSyncTime: DateTime.now(), // 记录同步时间
    );

    _saveDataFingerprints();
  }

  /// 更新数据指纹的同步时间
  void _updateSyncTime(String dataType) {
    final existing = dataFingerprints[dataType];
    if (existing != null) {
      dataFingerprints[dataType] = existing.copyWithSyncTime(DateTime.now());
      _saveDataFingerprints();
    }
  }

  /// 获取数据项数量
  int _getDataItemCount(Map<String, dynamic> data) {
    if (data.containsKey('novels') && data['novels'] is List) {
      return (data['novels'] as List).length;
    }
    if (data.containsKey('documents') && data['documents'] is List) {
      return (data['documents'] as List).length;
    }
    return 1; // 单个数据项
  }

  /// 获取AuthService实例
  AuthService get _getAuthService {
    _authService ??= Get.find<AuthService>();
    return _authService!;
  }

  /// 获取认证token
  Future<String?> _getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('auth_token');
    } catch (e) {
      print('❌ 获取token失败: $e');
      return null;
    }
  }

  /// 同步所有数据（增量同步）
  Future<bool> syncAllData() async {
    if (!_getAuthService.isLoggedIn.value) {
      print('❌ 用户未登录，无法同步');
      return false;
    }

    if (isSyncing.value) {
      print('⚠️ 同步正在进行中，跳过本次请求');
      return false;
    }

    try {
      isSyncing.value = true;
      syncProgress.value = 0.0;
      syncStatus.value = '正在检查数据变更...';

      print('🚀 开始增量数据同步...');

      // 收集所有需要同步的数据
      final dataToSync = await _collectDataForSync();

      if (dataToSync.isEmpty) {
        syncStatus.value = '数据已是最新，无需同步';
        print('✅ 所有数据都是最新的，无需同步');
        return true;
      }

      // 创建同步任务
      final tasks = _createSyncTasks(dataToSync);
      taskQueue.addAll(tasks);
      await _saveTaskQueue();

      // 执行同步任务
      bool allSuccess = true;
      int successCount = 0;
      int failedCount = 0;

      for (int i = 0; i < tasks.length; i++) {
        final task = tasks[i];
        syncStatus.value = '正在同步 ${task.type.name}...';
        syncProgress.value = i / tasks.length;

        final success = await _executeTask(task);
        if (success) {
          successCount++;
        } else {
          failedCount++;
          allSuccess = false;
        }
      }

      syncProgress.value = 1.0;

      // 更新同步状态
      if (allSuccess) {
        syncStatus.value = '同步完成';
        print('✅ 增量同步完成');
      } else if (successCount > 0) {
        syncStatus.value = '同步完成（部分失败）';
        print('⚠️ 增量同步完成（部分失败）- 成功: $successCount, 失败: $failedCount');
      } else {
        syncStatus.value = '同步失败';
        print('❌ 增量同步失败 - 所有任务都失败了');
        allSuccess = false;
      }

      // 如果有部分成功，仍然返回true，让用户知道有数据已经同步
      return successCount > 0;
    } catch (e) {
      print('❌ 增量同步失败: $e');
      syncStatus.value = '同步失败: ${e.toString()}';
      return false;
    } finally {
      isSyncing.value = false;
    }
  }

  /// 收集需要同步的数据
  Future<Map<SyncTaskType, Map<String, dynamic>>> _collectDataForSync() async {
    final Map<SyncTaskType, Map<String, dynamic>> dataToSync = {};

    try {
      // 检查用户设置
      final userSettings = await _collectUserSettings();
      if (_needsSync('userSettings', userSettings)) {
        dataToSync[SyncTaskType.userSettings] = userSettings;
      }

      // 检查小说数据（使用基于时间的增量同步）
      final novels = await _collectNovels();
      if (await _needsSyncByTime('novels', novels)) {
        // 只同步需要更新的小说
        final incrementalNovels = await _collectIncrementalNovels();
        print('🔍 增量小说收集结果: ${incrementalNovels['syncedNovels']}/${incrementalNovels['totalNovels']} 本小说需要同步');

        // 即使没有小说需要同步，也要记录这次检查（避免重复检查）
        if (incrementalNovels['data']['novels'].isNotEmpty || incrementalNovels['syncType'] == 'full') {
          // 转换为传统API期望的格式
          dataToSync[SyncTaskType.novels] = {
            'data': incrementalNovels['data'], // 保持原有的data结构
            'timestamp': incrementalNovels['timestamp'],
            'syncType': incrementalNovels['syncType'],
            'totalNovels': incrementalNovels['totalNovels'],
            'syncedNovels': incrementalNovels['syncedNovels'],
          };
          print('✅ 小说数据已添加到同步队列');
        } else {
          print('ℹ️ 没有小说需要同步，但检查过程已完成');
        }
      }

      // 检查知识库
      final knowledgeBase = await _collectKnowledgeBase();
      if (_needsSync('knowledgeBase', knowledgeBase)) {
        dataToSync[SyncTaskType.knowledgeBase] = knowledgeBase;
      }

      // 检查角色数据
      final characters = await _collectCharacters();
      if (_needsSync('characters', characters)) {
        dataToSync[SyncTaskType.characters] = characters;
      }

      // 检查文风包
      final writingStyles = await _collectWritingStyles();
      if (_needsSync('writingStyles', writingStyles)) {
        dataToSync[SyncTaskType.writingStyles] = writingStyles;
      }

      print('📊 发现 ${dataToSync.length} 个数据类型需要同步');
      return dataToSync;
    } catch (e) {
      print('❌ 收集同步数据失败: $e');
      return {};
    }
  }

  /// 创建同步任务
  List<SyncTask> _createSyncTasks(
      Map<SyncTaskType, Map<String, dynamic>> dataToSync) {
    final List<SyncTask> tasks = [];

    dataToSync.forEach((type, data) {
      final task = SyncTask(
        id: '${type.name}_${DateTime.now().millisecondsSinceEpoch}',
        type: type,
        data: data,
        createdAt: DateTime.now(),
      );
      tasks.add(task);
    });

    return tasks;
  }

  /// 执行同步任务
  Future<bool> _executeTask(SyncTask task) async {
    try {
      task.status = SyncTaskStatus.running;
      task.progress = 0.0;

      print('📤 执行任务: ${task.type.name}');

      // 检查数据是否为空
      if (task.data.isEmpty) {
        print('⚠️ 任务数据为空，跳过: ${task.type.name}');
        task.status = SyncTaskStatus.completed;
        task.completedAt = DateTime.now();
        return true;
      }

      // 使用CloudBase数据库API上传
      bool uploadSuccess = false;
      Map<String, dynamic>? uploadResult;

      try {
        uploadSuccess =
            await _uploadViaTraditionalAPI(task.data, task.type.name);
        if (uploadSuccess) {
          print('✅ CloudBase数据库上传成功: ${task.type.name}');
          uploadResult = {
            'fileId': 'db_upload_${DateTime.now().millisecondsSinceEpoch}',
            'fileSize': jsonEncode(task.data).length,
          };
        }
      } catch (e) {
        print('⚠️ CloudBase数据库上传失败: $e');
      }

      if (!uploadSuccess) {
        throw Exception('所有上传方式都失败');
      }

      task.progress = 1.0;
      task.status = SyncTaskStatus.completed;
      task.completedAt = DateTime.now();

      // 更新数据指纹
      _updateDataFingerprint(task.type.name, task.data);

      print('✅ 任务完成: ${task.type.name}');
      if (uploadResult != null) {
        print('📁 文件ID: ${uploadResult['fileId']}');
        print('📊 文件大小: ${uploadResult['fileSize']} bytes');
      }

      return true;
    } catch (e) {
      print('❌ 任务失败: ${task.type.name} - $e');

      task.status = SyncTaskStatus.failed;
      task.errorMessage = e.toString();

      // 重试逻辑
      if (task.retryCount < 3) {
        task.retryCount++;
        task.status = SyncTaskStatus.retrying;

        print('🔄 任务重试: ${task.type.name} (${task.retryCount}/3)');

        // 延迟后重试
        await Future.delayed(Duration(seconds: task.retryCount * 2));
        return await _executeTask(task);
      }

      return false;
    } finally {
      await _saveTaskQueue();
    }
  }

  /// 传统API上传方式（备用）
  Future<bool> _uploadViaTraditionalAPI(
      Map<String, dynamic> data, String dataType) async {
    try {
      final token = await _getToken();
      if (token == null) {
        print('❌ 获取token失败');
        return false;
      }

      // 检查数据大小，如果超过30KB则使用分块上传（降低阈值避免413错误）
      final dataString = jsonEncode({
        'data': dataType == 'novels'
            ? {'novels': data['data']['novels']}
            : {dataType: data},
        'timestamp': DateTime.now().toIso8601String(),
        'dataType': dataType
      });
      final dataSizeKB = dataString.length / 1024;

      print('📊 数据大小: ${dataSizeKB.toStringAsFixed(2)} KB');
      print('🔍 数据类型: $dataType');
      print('🔍 数据结构: ${data.keys.toList()}');

      if (dataSizeKB > 30) {
        print('📦 数据过大，使用分块上传策略');
        // 对于小说数据，提取实际的小说列表
        if (dataType == 'novels') {
          // 从task.data中提取小说列表
          final novelsData = data['data'];
          print('🔍 小说数据结构: ${novelsData?.runtimeType}');
          if (novelsData != null) {
            print('🔍 小说数据键: ${novelsData.keys.toList()}');
          }

          if (novelsData != null && novelsData['novels'] is List) {
            final novelsList = novelsData['novels'] as List;
            print('🔍 检测到小说数据，共 ${novelsList.length} 本小说，开始分块上传');
            return await _uploadNovelsInChunks(novelsList, token);
          } else {
            print('❌ 小说数据格式错误: ${novelsData.runtimeType}');
            print('❌ 期望格式: Map with "novels" key containing List');
            return false;
          }
        } else {
          // 其他类型暂时不支持分块，返回false让调用者处理
          print('⚠️ 暂不支持此数据类型的分块上传: $dataType, 数据类型: ${data.runtimeType}');
          return false;
        }
      }

      final response = await http
          .post(
            Uri.parse(ApiConfig.getEndpoint('syncUpload')),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: dataString,
          )
          .timeout(Duration(seconds: 60));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return responseData['success'] == true;
      } else {
        print('❌ 传统API上传失败: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ 传统API上传异常: $e');
      return false;
    }
  }

  /// 按章节分块上传小说数据 - 改为一个章节一个章节上传
  Future<bool> _uploadNovelsInChunks(List novels, String token) async {
    try {
      print('📚 开始按章节分块上传小说数据，共 ${novels.length} 本小说');

      for (int novelIndex = 0; novelIndex < novels.length; novelIndex++) {
        final novel = novels[novelIndex];
        print(
            '📖 处理小说: ${novel['title']} (${novelIndex + 1}/${novels.length})');

        if (novel['chapters'] != null && novel['chapters'] is List) {
          final chapters = novel['chapters'] as List;
          print('📄 小说共有 ${chapters.length} 个章节，开始逐章节上传');

          // 改为一个章节一个章节上传
          for (int chapterIndex = 0;
              chapterIndex < chapters.length;
              chapterIndex++) {
            final chapter = chapters[chapterIndex];

            final chapterTitle = chapter['title'] ?? '第${chapterIndex + 1}章';
            print(
                '📦 上传章节 ${chapterIndex + 1}/${chapters.length}: $chapterTitle');

            // 创建包含单个章节的小说数据
            final novelChunk = Map<String, dynamic>.from(novel);
            novelChunk['chapters'] = [chapter]; // 只包含当前章节
            novelChunk['chunkInfo'] = {
              'novelIndex': novelIndex,
              'chapterIndex': chapterIndex,
              'totalChapters': chapters.length,
              'chapterTitle': chapterTitle,
              'isLastChapter': chapterIndex == chapters.length - 1
            };

            // 检查章节数据大小，如果太大则进行内容分块
            final chapterDataSize = jsonEncode([novelChunk]).length / 1024;
            if (chapterDataSize > 30) {
              // 降低到40KB阈值，确保安全
              print(
                  '⚠️  章节数据过大 (${chapterDataSize.toStringAsFixed(2)} KB)，进行内容分块');
              final success = await _uploadLargeChapter(
                  novelChunk, token, chapterIndex, chapters.length);
              if (!success) {
                print('❌ 大章节上传失败: ${chapterIndex + 1}/${chapters.length}');
                return false;
              }
            } else {
              final success = await _uploadSingleChunk(
                  [novelChunk], 'novels', token, chapterIndex, chapters.length);
              if (!success) {
                print('❌ 章节上传失败: ${chapterIndex + 1}/${chapters.length}');
                return false;
              }
            }

            // 增加延迟避免请求过于频繁，并给服务器更多处理时间
            await Future.delayed(Duration(milliseconds: 1000));
          }
        } else {
          // 没有章节的小说直接上传
          print('📦 上传无章节小说: ${novel['title']}');
          final success =
              await _uploadSingleChunk([novel], 'novels', token, 0, 1);
          if (!success) {
            print('❌ 小说上传失败: ${novel['title']}');
            return false;
          }
        }

        // 每本小说之间也增加延迟
        if (novelIndex < novels.length - 1) {
          await Future.delayed(Duration(milliseconds: 500));
        }
      }

      print('✅ 所有小说章节分块上传完成');
      return true;
    } catch (e) {
      print('❌ 小说分块上传异常: $e');
      return false;
    }
  }

  /// 上传超大章节（对章节内容进行分块）
  Future<bool> _uploadLargeChapter(Map<String, dynamic> novelChunk,
      String token, int chapterIndex, int totalChapters) async {
    try {
      final chapter = novelChunk['chapters'][0];
      final chapterContent = chapter['content'] ?? '';

      if (chapterContent.isEmpty) {
        // 如果没有内容，直接上传
        return await _uploadSingleChunk(
            [novelChunk], 'novels', token, chapterIndex, totalChapters);
      }

      // 将章节内容按字符数分块（避免UTF-8编码问题）
      const maxChunkChars = 2000; // 进一步减小到2000个字符（约6KB），确保不超过限制
      final totalContentChunks = (chapterContent.length / maxChunkChars).ceil();

      print('📄 章节内容分为 $totalContentChunks 个内容块');

      for (int contentChunkIndex = 0;
          contentChunkIndex < totalContentChunks;
          contentChunkIndex++) {
        final startChar = contentChunkIndex * maxChunkChars;
        final endChar =
            (startChar + maxChunkChars).clamp(0, chapterContent.length);
        final chunkContent = chapterContent.substring(startChar, endChar);

        // 创建内容分块 - 只包含必要的数据
        final contentChunk = {
          'title': chapter['title'],
          'content': chunkContent,
          'contentChunkInfo': {
            'chunkIndex': contentChunkIndex,
            'totalChunks': totalContentChunks,
            'isLastChunk': contentChunkIndex == totalContentChunks - 1
          }
        };

        // 只包含必要的小说信息，避免传输大量元数据
        final chunkNovelData = {
          'title': novelChunk['title'],
          'id': novelChunk['id'],
          'chapters': [contentChunk],
          'chunkInfo': {
            ...novelChunk['chunkInfo'],
            'contentChunkIndex': contentChunkIndex,
            'totalContentChunks': totalContentChunks,
            'originalChapterIndex': chapterIndex, // 保持原始章节索引
          }
        };

        print('📦 上传内容块 ${contentChunkIndex + 1}/$totalContentChunks');

        final success = await _uploadSingleChunk(
            [chunkNovelData],
            'novels',
            token,
            chapterIndex * 1000 + contentChunkIndex, // HTTP请求索引（避免冲突）
            totalChapters);

        if (!success) {
          print('❌ 内容块上传失败: ${contentChunkIndex + 1}/$totalContentChunks');
          return false;
        }

        // 内容块间延迟
        await Future.delayed(Duration(milliseconds: 800));
      }

      print('✅ 大章节分块上传完成');
      return true;
    } catch (e) {
      print('❌ 大章节上传异常: $e');
      return false;
    }
  }



  /// 上传单个数据块（带重试机制）
  Future<bool> _uploadSingleChunk(dynamic chunkData, String dataType,
      String token, int chunkIndex, int totalChunks) async {
    const maxRetries = 3;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // 计算数据大小用于日志
        final dataSize = jsonEncode({
              'data': dataType == 'novels'
                  ? {'novels': chunkData}
                  : {dataType: chunkData},
              'timestamp': DateTime.now().toIso8601String(),
              'dataType': dataType,
              'chunkInfo': {
                'index': chunkIndex,
                'total': totalChunks,
                'type': 'novels_chunk_$chunkIndex',
                'isChunked': true
              }
            }).length /
            1024;

        if (attempt > 1) {
          print(
              '🔄 重试上传 (${attempt}/$maxRetries) - 数据大小: ${dataSize.toStringAsFixed(2)} KB');
        }

        final response = await http
            .post(
              Uri.parse(ApiConfig.getEndpoint('syncUpload')),
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer $token',
              },
              body: jsonEncode({
                'data': dataType == 'novels'
                    ? {'novels': chunkData}
                    : {dataType: chunkData},
                'timestamp': DateTime.now().toIso8601String(),
                'dataType': dataType,
                'chunkInfo': {
                  'index': chunkIndex,
                  'total': totalChunks,
                  'type': 'novels_chunk_$chunkIndex',
                  'isChunked': true
                }
              }),
            )
            .timeout(Duration(seconds: 60)); // 增加到60秒超时

        if (response.statusCode == 200) {
          final responseData = jsonDecode(response.body);
          if (responseData['success'] == true) {
            print(
                '✅ 数据块上传成功: ${chunkIndex + 1}/$totalChunks (${dataSize.toStringAsFixed(2)} KB)');
            return true;
          }
        }

        print('❌ 数据块上传失败: ${response.statusCode} (尝试 ${attempt}/$maxRetries)');

        // 如果不是最后一次尝试，等待后重试
        if (attempt < maxRetries) {
          await Future.delayed(Duration(seconds: attempt * 2)); // 递增延迟
        }
      } catch (e) {
        print('❌ 数据块上传异常: $e (尝试 ${attempt}/$maxRetries)');

        // 如果不是最后一次尝试，等待后重试
        if (attempt < maxRetries) {
          await Future.delayed(Duration(seconds: attempt * 2)); // 递增延迟
        }
      }
    }

    return false; // 所有重试都失败
  }

  // CloudBase直传方法已删除 - 统一使用数据库API

  /// 收集用户设置数据
  Future<Map<String, dynamic>> _collectUserSettings() async {
    try {
      // 使用UserSyncService的数据收集方法
      final userSyncService = Get.find<UserSyncService>();
      final allData = await userSyncService.collectAllUserData();

      if (allData != null) {
        // 只返回用户设置部分
        return {
          'type': 'userSettings',
          'data': {
            'userSettings': allData['userSettings'] ?? {},
          },
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      return {};
    } catch (e) {
      print('❌ 收集用户设置失败: $e');
      return {};
    }
  }

  /// 收集小说数据
  Future<Map<String, dynamic>> _collectNovels() async {
    try {
      // 使用UserSyncService的数据收集方法
      final userSyncService = Get.find<UserSyncService>();
      final allData = await userSyncService.collectAllUserData();

      if (allData != null) {
        return {
          'type': 'novels',
          'data': {
            'novels': allData['novels'] ?? [],
          },
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      return {};
    } catch (e) {
      print('❌ 收集小说数据失败: $e');
      return {};
    }
  }

  /// 收集增量小说数据（只包含新建或修改的小说）
  Future<Map<String, dynamic>> _collectIncrementalNovels() async {
    try {
      print('📚 开始收集增量小说数据...');

      // 获取所有小说数据
      final allNovelsData = await _collectNovels();
      final allNovels = allNovelsData['data']['novels'] as List;
      print('📖 本地共有 ${allNovels.length} 本小说');

      // 获取上次同步时间
      final existingFingerprint = dataFingerprints['novels'];
      final lastSyncTime = existingFingerprint?.lastSyncTime;
      print('⏰ 上次同步时间: ${lastSyncTime?.toString() ?? "从未同步"}');

      List<Map<String, dynamic>> novelsToSync = [];

      if (lastSyncTime == null) {
        // 首次同步，同步所有小说
        print('🔍 首次同步，需要同步所有 ${allNovels.length} 本小说');
        novelsToSync = allNovels.cast<Map<String, dynamic>>();
        for (final novel in novelsToSync) {
          print('📝 需要同步: ${novel['title']} (首次同步)');
        }
      } else {
        // 获取云端数据进行对比
        print('🔍 获取云端数据进行对比...');
        final cloudData = await _getCloudData();
        final cloudNovels = cloudData?['novels'] as List?;
        print('☁️ 云端小说数量: ${cloudNovels?.length ?? 0}');

        if (cloudNovels == null || cloudNovels.isEmpty) {
          // 云端无数据，同步所有本地小说
          print('🔍 云端无数据，需要同步所有本地小说到云端...');
          novelsToSync = allNovels.cast<Map<String, dynamic>>();
          for (final novel in novelsToSync) {
            print('📝 需要同步: ${novel['title']} (云端不存在，需要上传)');
          }
        } else {
          // 对比本地和云端数据
          print('🔍 对比本地 ${allNovels.length} 本小说与云端 ${cloudNovels.length} 本小说...');
          for (final localNovel in allNovels) {
            final syncReason = _compareNovelWithCloud(localNovel, cloudNovels);
            if (syncReason != null) {
              novelsToSync.add(localNovel);
              print('📝 需要同步: ${localNovel['title']} ($syncReason)');
            } else {
              print('✅ 无需同步: ${localNovel['title']} (已是最新)');
            }
          }
        }

        print('📊 同步结果: ${novelsToSync.length}/${allNovels.length} 本小说需要同步');
      }

      return {
        'type': 'novels',
        'data': {
          'novels': novelsToSync,
        },
        'timestamp': DateTime.now().toIso8601String(),
        'syncType': lastSyncTime == null ? 'full' : 'incremental',
        'totalNovels': allNovels.length,
        'syncedNovels': novelsToSync.length,
      };
    } catch (e) {
      print('❌ 收集增量小说数据失败: $e');
      return {
        'type': 'novels',
        'data': {
          'novels': [],
        },
        'timestamp': DateTime.now().toIso8601String(),
        'syncType': 'error',
      };
    }
  }

  /// 获取小说修改原因（用于调试）
  String _getNovelModificationReason(
      Map<String, dynamic> novel, DateTime lastSyncTime) {
    List<String> reasons = [];

    // 检查是否是新建小说（创建时间晚于上次同步）
    final createdAtStr = novel['createdAt'] as String?;
    if (createdAtStr != null) {
      final createdAt = DateTime.parse(createdAtStr);
      if (createdAt.isAfter(lastSyncTime)) {
        reasons.add('新建小说');
        return reasons.join(', '); // 新建小说直接返回
      }
    }

    // 检查小说本身的更新时间
    final updatedAtStr = novel['updatedAt'] as String?;
    if (updatedAtStr != null) {
      final updatedAt = DateTime.parse(updatedAtStr);
      if (updatedAt.isAfter(lastSyncTime)) {
        reasons.add('小说内容更新');
      }
    }

    // 检查章节修改
    final chapters = novel['chapters'] as List?;
    if (chapters != null) {
      int newChapters = 0;
      int modifiedChapters = 0;

      for (final chapter in chapters) {
        // 优先使用 createTime，如果有 createdAt 则使用 createdAt
        String? chapterCreatedStr = chapter['createdAt'] as String?;
        chapterCreatedStr ??= chapter['createTime'] as String?;

        String? chapterUpdatedStr = chapter['updatedAt'] as String?;

        // 检查新增章节
        if (chapterCreatedStr != null) {
          try {
            final chapterCreated = DateTime.parse(chapterCreatedStr);
            if (chapterCreated.isAfter(lastSyncTime)) {
              newChapters++;
              continue; // 新增章节不需要再检查修改
            }
          } catch (e) {
            // 时间解析失败，当作新章节处理
            newChapters++;
            continue;
          }
        }

        // 检查修改章节（如果有 updatedAt 字段）
        if (chapterUpdatedStr != null) {
          try {
            final chapterUpdated = DateTime.parse(chapterUpdatedStr);
            if (chapterUpdated.isAfter(lastSyncTime)) {
              modifiedChapters++;
            }
          } catch (e) {
            // 时间解析失败，当作修改章节处理
            modifiedChapters++;
          }
        }
      }

      if (newChapters > 0) {
        reasons.add('新增${newChapters}章');
      }
      if (modifiedChapters > 0) {
        reasons.add('修改${modifiedChapters}章');
      }
    }

    return reasons.isNotEmpty ? reasons.join(', ') : '时间检查';
  }

  /// 收集知识库数据
  Future<Map<String, dynamic>> _collectKnowledgeBase() async {
    try {
      // 使用UserSyncService的数据收集方法
      final userSyncService = Get.find<UserSyncService>();
      final allData = await userSyncService.collectAllUserData();

      if (allData != null) {
        return {
          'type': 'knowledgeBase',
          'data': {
            'knowledgeDocuments': allData['knowledgeDocuments'] ?? [],
          },
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      return {};
    } catch (e) {
      print('❌ 收集知识库数据失败: $e');
      return {};
    }
  }

  /// 收集角色数据
  Future<Map<String, dynamic>> _collectCharacters() async {
    try {
      // 使用UserSyncService的数据收集方法
      final userSyncService = Get.find<UserSyncService>();
      final allData = await userSyncService.collectAllUserData();

      if (allData != null) {
        return {
          'type': 'characters',
          'data': {
            'characterCards': allData['characterCards'] ?? [],
            'characterTypes': allData['characterTypes'] ?? [],
          },
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      return {};
    } catch (e) {
      print('❌ 收集角色数据失败: $e');
      return {};
    }
  }

  /// 收集文风包数据
  Future<Map<String, dynamic>> _collectWritingStyles() async {
    try {
      // 使用UserSyncService的数据收集方法
      final userSyncService = Get.find<UserSyncService>();
      final allData = await userSyncService.collectAllUserData();

      if (allData != null) {
        return {
          'type': 'writingStyles',
          'data': {
            'writingStylePackages': allData['writingStylePackages'] ?? [],
          },
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      return {};
    } catch (e) {
      return {};
    }
  }

  /// 手动触发同步特定数据类型
  Future<bool> syncDataType(SyncTaskType type) async {
    if (!_getAuthService.isLoggedIn.value) {
      print('❌ 用户未登录，无法同步');
      return false;
    }

    try {
      syncStatus.value = '正在同步 ${type.name}...';

      Map<String, dynamic> data;
      switch (type) {
        case SyncTaskType.userSettings:
          data = await _collectUserSettings();
          break;
        case SyncTaskType.novels:
          data = await _collectNovels();
          break;
        case SyncTaskType.knowledgeBase:
          data = await _collectKnowledgeBase();
          break;
        case SyncTaskType.characters:
          data = await _collectCharacters();
          break;
        case SyncTaskType.writingStyles:
          data = await _collectWritingStyles();
          break;
        case SyncTaskType.full:
          return await syncAllData();
      }

      if (data.isEmpty) {
        syncStatus.value = '没有数据需要同步';
        return true;
      }

      final task = SyncTask(
        id: '${type.name}_${DateTime.now().millisecondsSinceEpoch}',
        type: type,
        data: data,
        createdAt: DateTime.now(),
      );

      final success = await _executeTask(task);
      syncStatus.value = success ? '同步完成' : '同步失败';

      return success;
    } catch (e) {
      print('❌ 同步数据类型失败: $e');
      syncStatus.value = '同步失败: ${e.toString()}';
      return false;
    }
  }

  /// 清理已完成的任务
  void cleanupCompletedTasks() {
    final now = DateTime.now();
    taskQueue.removeWhere((task) =>
        task.status == SyncTaskStatus.completed &&
        task.completedAt != null &&
        now.difference(task.completedAt!).inHours > 24);
    _saveTaskQueue();
  }

  /// 获取同步统计信息
  Map<String, dynamic> getSyncStats() {
    final total = taskQueue.length;
    final completed =
        taskQueue.where((t) => t.status == SyncTaskStatus.completed).length;
    final failed =
        taskQueue.where((t) => t.status == SyncTaskStatus.failed).length;
    final running =
        taskQueue.where((t) => t.status == SyncTaskStatus.running).length;

    return {
      'total': total,
      'completed': completed,
      'failed': failed,
      'running': running,
      'lastSyncTime': dataFingerprints.values.isNotEmpty
          ? dataFingerprints.values
              .map((f) => f.lastModified)
              .reduce((a, b) => a.isAfter(b) ? a : b)
          : null,
    };
  }
}
