import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/writing_style_package_controller.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:novel_app/widgets/writing_style_package_card.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_selector/file_selector.dart';
import 'dart:io';
import 'package:uuid/uuid.dart';

class WritingStylePackageScreen extends StatelessWidget {
  const WritingStylePackageScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WritingStylePackageController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('文风包管理'),
        backgroundColor: const Color(0xFF4CAF50),
        actions: [
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: '导入文风包',
            onPressed: () => _importPackages(context),
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: '导出文风包',
            onPressed: () => _exportPackages(context),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.packages.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.style,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  '还没有文风包',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  '点击右下角按钮添加文风包',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: _getCrossAxisCount(context),
              childAspectRatio: 1.0, // 正方形
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: controller.packages.length,
            itemBuilder: (context, index) {
              final package = controller.packages[index];
              return WritingStylePackageCard(
                package: package,
                onTap: () => _showPackageDetail(package),
                onEdit: () => _showEditPackageDialog(context, package),
                onDelete: () => _showDeleteConfirmation(context, package),
              );
            },
          ),
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddPackageDialog(context),
        backgroundColor: const Color(0xFF8B4513),
        child: const Icon(Icons.add),
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }

  void _showPackageDetail(WritingStylePackage package) {
    Get.dialog(
      WritingStylePackageDetailDialog(package: package),
    );
  }

  Future<void> _importPackages(BuildContext context) async {
    try {
      // 定义允许的文件类型
      final typeGroup = XTypeGroup(
        label: 'JSON文件',
        extensions: ['json'],
      );

      // 打开文件选择器
      final List<XFile> files = await openFiles(
        acceptedTypeGroups: [typeGroup],
        confirmButtonText: '选择',
      );

      if (files.isNotEmpty) {
        final controller = Get.find<WritingStylePackageController>();
        final importedPackages = <WritingStylePackage>[];

        for (final file in files) {
          final bytes = await file.readAsBytes();
          final jsonStr = utf8.decode(bytes);
          final jsonData = json.decode(jsonStr);

          if (jsonData is List) {
            for (final item in jsonData) {
              try {
                final package = WritingStylePackage.fromJson(item);
                importedPackages.add(package);
              } catch (e) {
                // 静默处理解析失败
              }
            }
          } else if (jsonData is Map<String, dynamic>) {
            try {
              final package = WritingStylePackage.fromJson(jsonData);
              importedPackages.add(package);
            } catch (e) {
              // 静默处理解析失败
            }
          }
        }

        if (importedPackages.isNotEmpty) {
          await controller.importPackages(importedPackages);
          Get.snackbar(
            '导入成功',
            '成功导入 ${importedPackages.length} 个文风包',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      }
    } catch (e) {
      print('导入文风包失败: $e');
      Get.snackbar(
        '导入失败',
        '导入文风包时发生错误: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _exportPackages(BuildContext context) async {
    try {
      final controller = Get.find<WritingStylePackageController>();

      if (controller.packages.isEmpty) {
        Get.snackbar(
          '导出失败',
          '没有可导出的文风包',
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // 将文风包转换为JSON
      final jsonData = controller.packages.map((p) => p.toJson()).toList();
      final jsonStr = json.encode(jsonData);

      // 创建临时文件
      final tempDir = Directory.systemTemp;
      final file = File('${tempDir.path}/writing_style_packages.json');
      await file.writeAsString(jsonStr);

      // 分享文件
      await Share.shareXFiles(
        [XFile(file.path)],
        subject: '文风包导出',
      );

      Get.snackbar(
        '导出成功',
        '文风包已准备分享',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      print('导出文风包失败: $e');
      Get.snackbar(
        '导出失败',
        '导出文风包时发生错误: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }



  void _showAddPackageDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    final authorController = TextEditingController();
    final sampleTextsController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加文风包'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(labelText: '名称'),
              ),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(labelText: '描述'),
                maxLines: 2,
              ),
              TextField(
                controller: authorController,
                decoration: const InputDecoration(labelText: '作者'),
              ),
              TextField(
                controller: sampleTextsController,
                decoration: const InputDecoration(
                  labelText: '示例文本（每行一个）',
                  hintText: '请输入示例文本，每行一个',
                ),
                maxLines: 5,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final package = WritingStylePackage(
                id: const Uuid().v4(),
                name: nameController.text,
                description: descriptionController.text,
                author: authorController.text,
                sampleTexts: sampleTextsController.text
                    .split('\n')
                    .where((text) => text.isNotEmpty)
                    .toList(),
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              );
              Get.find<WritingStylePackageController>().addPackage(package);
              Navigator.pop(context);
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  void _showEditPackageDialog(
      BuildContext context, WritingStylePackage package) {
    final nameController = TextEditingController(text: package.name);
    final descriptionController =
        TextEditingController(text: package.description);
    final authorController = TextEditingController(text: package.author);
    final sampleTextsController = TextEditingController(
      text: package.sampleTexts.join('\n'),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑文风包'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(labelText: '名称'),
              ),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(labelText: '描述'),
                maxLines: 2,
              ),
              TextField(
                controller: authorController,
                decoration: const InputDecoration(labelText: '作者'),
              ),
              TextField(
                controller: sampleTextsController,
                decoration: const InputDecoration(
                  labelText: '示例文本（每行一个）',
                  hintText: '请输入示例文本，每行一个',
                ),
                maxLines: 5,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final updatedPackage = WritingStylePackage(
                id: package.id,
                name: nameController.text,
                description: descriptionController.text,
                author: authorController.text,
                sampleTexts: sampleTextsController.text
                    .split('\n')
                    .where((text) => text.isNotEmpty)
                    .toList(),
                createdAt: package.createdAt,
                updatedAt: DateTime.now(),
              );
              final controller = Get.find<WritingStylePackageController>();
              controller.removePackage(package.id);
              controller.addPackage(updatedPackage);
              Navigator.pop(context);
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(
      BuildContext context, WritingStylePackage package) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除文风包"${package.name}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.find<WritingStylePackageController>()
                  .removePackage(package.id);
              Navigator.pop(context);
            },
            child: const Text(
              '删除',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
